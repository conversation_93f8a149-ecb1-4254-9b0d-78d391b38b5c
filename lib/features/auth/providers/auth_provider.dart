import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/models/user.dart';

class AuthState {
  final User? user;
  final bool isLoading;
  final String? error;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  final SupabaseClient _supabase;

  AuthNotifier(this._supabase) : super(const AuthState()) {
    _init();
  }

  void _init() {
    // Listen to auth state changes
    _supabase.auth.onAuthStateChange.listen((data) {
      final session = data.session;
      if (session?.user != null) {
        _loadUserProfile(session!.user.id);
      } else {
        state = state.copyWith(user: null);
      }
    });

    // Check if user is already signed in
    final currentUser = _supabase.auth.currentUser;
    if (currentUser != null) {
      _loadUserProfile(currentUser.id);
    }
  }

  Future<void> signInAnonymously() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _supabase.auth.signInAnonymously();
      
      if (response.user != null) {
        // Create user profile in database
        await _createUserProfile(response.user!.id);
        await _loadUserProfile(response.user!.id);
      }

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> _createUserProfile(String userId) async {
    try {
      // Check if user profile already exists
      final existingUser = await _supabase
          .from('users')
          .select()
          .eq('id', userId)
          .maybeSingle();

      if (existingUser == null) {
        // Create new user profile
        await _supabase.from('users').insert({
          'id': userId,
          'level': 1,
          'xp': 0,
        });

        // Award newcomer medal
        await _awardNewcomerMedal(userId);
      }
    } catch (e) {
      print('Error creating user profile: $e');
      // Don't throw - user can still use the app
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      final userData = await _supabase
          .from('users')
          .select()
          .eq('id', userId)
          .single();

      final user = User.fromJson(userData);
      state = state.copyWith(user: user);
    } catch (e) {
      print('Error loading user profile: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> _awardNewcomerMedal(String userId) async {
    try {
      await _supabase.functions.invoke(
        'award-medals',
        body: {
          'user_id': userId,
          'event_type': 'registration',
        },
      );
    } catch (e) {
      print('Error awarding newcomer medal: $e');
    }
  }

  Future<void> signOut() async {
    await _supabase.auth.signOut();
    state = const AuthState();
  }

  void updateUser(User updatedUser) {
    state = state.copyWith(user: updatedUser);
  }
}

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(Supabase.instance.client);
});
