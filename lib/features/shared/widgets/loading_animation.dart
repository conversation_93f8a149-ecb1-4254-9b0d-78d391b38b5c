import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class LoadingAnimation extends StatelessWidget {
  final double size;

  const LoadingAnimation({
    super.key,
    this.size = 48,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Placeholder for running 猹 animation
          // TODO: Replace with actual pixel art animation
          Text(
            '🐹',
            style: TextStyle(fontSize: size * 0.8),
          )
              .animate(onPlay: (controller) => controller.repeat())
              .slideX(
                begin: -0.5,
                end: 0.5,
                duration: 1000.ms,
                curve: Curves.easeInOut,
              )
              .then()
              .slideX(
                begin: 0.5,
                end: -0.5,
                duration: 1000.ms,
                curve: Curves.easeInOut,
              ),
        ],
      ),
    );
  }
}
