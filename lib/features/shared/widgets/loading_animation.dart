import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

class LoadingAnimation extends StatelessWidget {
  final double size;

  const LoadingAnimation({
    super.key,
    this.size = 48,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size * 2,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Running track background
          Container(
            width: size * 2,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.secondaryText.withOpacity(0.2),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Running 猹 (using hamster emoji as placeholder)
          Positioned(
            child: Text(
              '🐹',
              style: TextStyle(fontSize: size * 0.6),
            )
                .animate(onPlay: (controller) => controller.repeat())
                .slideX(
                  begin: -1.0,
                  end: 1.0,
                  duration: 1500.ms,
                  curve: Curves.easeInOut,
                )
                .scale(
                  begin: 1.0,
                  end: 1.1,
                  duration: 750.ms,
                )
                .then()
                .scale(
                  begin: 1.1,
                  end: 1.0,
                  duration: 750.ms,
                ),
          ),

          // Dust particles
          ...List.generate(3, (index) {
            return Positioned(
              child: Container(
                width: 2,
                height: 2,
                decoration: BoxDecoration(
                  color: AppColors.guatianGreen.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
              )
                  .animate(onPlay: (controller) => controller.repeat())
                  .slideX(
                    begin: -0.8 + (index * 0.2),
                    end: 0.8 + (index * 0.2),
                    duration: (1500 + index * 200).ms,
                    curve: Curves.easeInOut,
                  )
                  .fadeIn(duration: 300.ms)
                  .then()
                  .fadeOut(duration: 300.ms),
            );
          }),
        ],
      ),
    );
  }
}
