import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/models/medal.dart';
import '../../../core/theme/app_theme.dart';

class MedalWall extends StatelessWidget {
  final List<Medal> allMedals;
  final List<UserMedal> userMedals;

  const MedalWall({
    super.key,
    required this.allMedals,
    required this.userMedals,
  });

  @override
  Widget build(BuildContext context) {
    final userMedalIds = userMedals.map((um) => um.medalId).toSet();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: allMedals.length,
      itemBuilder: (context, index) {
        final medal = allMedals[index];
        final isEarned = userMedalIds.contains(medal.id);
        final userMedal = userMedals.firstWhere(
          (um) => um.medalId == medal.id,
          orElse: () => UserMedal(
            id: '',
            userId: '',
            medalId: medal.id,
            achievedAt: DateTime.now(),
          ),
        );

        return GestureDetector(
          onTap: () => _showMedalDetails(context, medal, userMedal, isEarned),
          child: _buildMedalItem(medal, isEarned),
        );
      },
    );
  }

  Widget _buildMedalItem(Medal medal, bool isEarned) {
    return Container(
      decoration: BoxDecoration(
        color: isEarned 
            ? _getMedalColor(medal.medalType).withOpacity(0.2)
            : AppColors.secondaryText.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isEarned 
              ? _getMedalColor(medal.medalType)
              : AppColors.secondaryText.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Medal icon placeholder
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isEarned 
                  ? _getMedalColor(medal.medalType)
                  : AppColors.secondaryText,
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getMedalIcon(medal.name),
              color: isEarned ? Colors.black : AppColors.primaryBackground,
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          
          // Medal name
          Text(
            medal.name,
            style: TextStyle(
              fontSize: 10,
              color: isEarned 
                  ? AppColors.primaryText
                  : AppColors.secondaryText,
              fontWeight: isEarned ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    ).animate(target: isEarned ? 1 : 0)
     .shimmer(duration: 2000.ms, color: _getMedalColor(medal.medalType))
     .scale(begin: 1.0, end: 1.05);
  }

  Color _getMedalColor(String medalType) {
    switch (medalType) {
      case 'bronze':
        return const Color(0xFFCD7F32);
      case 'silver':
        return const Color(0xFFC0C0C0);
      case 'gold':
        return AppColors.hotMelonYellow;
      case 'diamond':
        return const Color(0xFFB9F2FF);
      default:
        return AppColors.guatianGreen;
    }
  }

  IconData _getMedalIcon(String medalName) {
    if (medalName.contains('第一口瓜')) return Icons.restaurant;
    if (medalName.contains('勤劳')) return Icons.work;
    if (medalName.contains('闰土')) return Icons.agriculture;
    if (medalName.contains('承包户')) return Icons.business;
    if (medalName.contains('故事')) return Icons.book;
    if (medalName.contains('热瓜')) return Icons.local_fire_department;
    if (medalName.contains('爆款')) return Icons.trending_up;
    if (medalName.contains('瓜神')) return Icons.star;
    if (medalName.contains('逻辑')) return Icons.psychology;
    if (medalName.contains('共鸣')) return Icons.favorite;
    if (medalName.contains('Drama')) return Icons.theater_comedy;
    if (medalName.contains('开荒')) return Icons.flag;
    if (medalName.contains('劳模')) return Icons.how_to_vote;
    if (medalName.contains('常青树')) return Icons.eco;
    if (medalName.contains('表情包')) return Icons.emoji_emotions;
    if (medalName.contains('新人')) return Icons.person_add;
    return Icons.emoji_events;
  }

  void _showMedalDetails(BuildContext context, Medal medal, UserMedal? userMedal, bool isEarned) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Medal icon
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: isEarned 
                    ? _getMedalColor(medal.medalType)
                    : AppColors.secondaryText,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getMedalIcon(medal.name),
                color: isEarned ? Colors.black : AppColors.primaryBackground,
                size: 32,
              ),
            ),
            const SizedBox(height: 16),
            
            // Medal name
            Text(
              medal.name,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: isEarned 
                    ? _getMedalColor(medal.medalType)
                    : AppColors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            
            // Medal description
            Text(
              medal.description,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            
            if (isEarned && userMedal != null) ...[
              const SizedBox(height: 12),
              Text(
                '获得时间: ${_formatDate(userMedal.achievedAt)}',
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
  }
}
