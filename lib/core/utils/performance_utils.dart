import 'dart:async';
import 'package:flutter/foundation.dart';

class PerformanceUtils {
  static const int _defaultDebounceMs = 300;
  static const int _defaultThrottleMs = 1000;

  /// Debounce function calls to prevent excessive API calls
  static Timer? _debounceTimer;
  
  static void debounce(VoidCallback callback, {int milliseconds = _defaultDebounceMs}) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(Duration(milliseconds: milliseconds), callback);
  }

  /// Throttle function calls to limit frequency
  static DateTime? _lastThrottleTime;
  
  static bool throttle({int milliseconds = _defaultThrottleMs}) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!).inMilliseconds >= milliseconds) {
      _lastThrottleTime = now;
      return true;
    }
    return false;
  }

  /// Measure execution time of a function
  static Future<T> measureAsync<T>(
    String label,
    Future<T> Function() function,
  ) async {
    if (!kDebugMode) {
      return await function();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      debugPrint('⏱️ $label took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ $label failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Measure execution time of a synchronous function
  static T measureSync<T>(
    String label,
    T Function() function,
  ) {
    if (!kDebugMode) {
      return function();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = function();
      stopwatch.stop();
      debugPrint('⏱️ $label took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ $label failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Batch multiple operations to reduce overhead
  static Future<List<T>> batchOperations<T>(
    List<Future<T> Function()> operations, {
    int batchSize = 5,
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    final results = <T>[];
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      final batchResults = await Future.wait(
        batch.map((op) => op()),
      );
      results.addAll(batchResults);
      
      // Add delay between batches to prevent overwhelming the server
      if (i + batchSize < operations.length) {
        await Future.delayed(delay);
      }
    }
    
    return results;
  }

  /// Cache for expensive computations
  static final Map<String, _CacheEntry> _cache = {};
  
  static T? getCached<T>(String key) {
    final entry = _cache[key];
    if (entry != null && !entry.isExpired) {
      return entry.value as T;
    }
    _cache.remove(key);
    return null;
  }

  static void setCached<T>(
    String key, 
    T value, {
    Duration ttl = const Duration(minutes: 5),
  }) {
    _cache[key] = _CacheEntry(value, DateTime.now().add(ttl));
  }

  static void clearCache() {
    _cache.clear();
  }

  /// Memory usage monitoring (debug only)
  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      // Note: Actual memory monitoring would require platform-specific code
      debugPrint('📊 Memory check at: $context');
    }
  }

  /// Cleanup resources
  static void dispose() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
    _lastThrottleTime = null;
    clearCache();
  }
}

class _CacheEntry {
  final dynamic value;
  final DateTime expiresAt;

  _CacheEntry(this.value, this.expiresAt);

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

/// Mixin for widgets that need performance optimizations
mixin PerformanceOptimizedWidget {
  void debounceCallback(VoidCallback callback, {int milliseconds = 300}) {
    PerformanceUtils.debounce(callback, milliseconds: milliseconds);
  }

  bool shouldThrottle({int milliseconds = 1000}) {
    return PerformanceUtils.throttle(milliseconds: milliseconds);
  }
}

/// Custom scroll physics for better performance
class OptimizedScrollPhysics extends ClampingScrollPhysics {
  const OptimizedScrollPhysics({ScrollPhysics? parent}) : super(parent: parent);

  @override
  OptimizedScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return OptimizedScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double get minFlingVelocity => 100.0; // Reduced for better control

  @override
  double get maxFlingVelocity => 2000.0; // Reduced for better performance
}
