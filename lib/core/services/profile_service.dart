import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/medal.dart';

class ProfileService {
  final SupabaseClient _supabase;

  ProfileService(this._supabase);

  Future<List<Medal>> getAllMedals() async {
    final response = await _supabase
        .from('medals')
        .select()
        .order('id');

    return response.map<Medal>((data) => Medal.fromJson(data)).toList();
  }

  Future<List<UserMedal>> getUserMedals() async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('user_medals')
        .select('''
          *,
          medals(*)
        ''')
        .eq('user_id', user.id)
        .order('achieved_at', ascending: false);

    return response.map<UserMedal>((data) {
      return UserMedal.fromJson({
        ...data,
        'medal': data['medals'],
      });
    }).toList();
  }

  Future<Map<String, int>> getUserStats() async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    // Get stories count and total heat
    final storiesResponse = await _supabase
        .from('stories')
        .select('heat')
        .eq('author_id', user.id);

    final storiesCount = storiesResponse.length;
    final totalHeat = storiesResponse.fold<int>(
      0, 
      (sum, story) => sum + (story['heat'] as int? ?? 0),
    );

    // Get votes count (votes made by user)
    final votesResponse = await _supabase
        .from('votes')
        .select('id')
        .eq('user_id', user.id);

    final votesCount = votesResponse.length;

    // Get total likes (simplified - count of positive reactions to user's stories)
    final likesResponse = await _supabase
        .from('reactions')
        .select('id')
        .in_('story_id', storiesResponse.map((s) => s['id']).toList())
        .in_('emoji_text', ['👍', '❤️', '🔥', '💯']);

    final totalLikes = likesResponse.length;

    return {
      'stories_count': storiesCount,
      'total_heat': totalHeat,
      'votes_count': votesCount,
      'total_likes': totalLikes,
    };
  }
}

final profileServiceProvider = Provider<ProfileService>((ref) {
  return ProfileService(Supabase.instance.client);
});
