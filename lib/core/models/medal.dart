import 'package:json_annotation/json_annotation.dart';

part 'medal.g.dart';

@JsonSerializable()
class Medal {
  final int id;
  final String name;
  final String description;
  @JsonKey(name: 'icon_url')
  final String iconUrl;

  const Medal({
    required this.id,
    required this.name,
    required this.description,
    required this.iconUrl,
  });

  factory Medal.fromJson(Map<String, dynamic> json) => _$MedalFromJson(json);
  Map<String, dynamic> toJson() => _$MedalToJson(this);

  Medal copyWith({
    int? id,
    String? name,
    String? description,
    String? iconUrl,
  }) {
    return Medal(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Medal &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Medal{id: $id, name: $name}';
  }
}

@JsonSerializable()
class UserMedal {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'medal_id')
  final int medalId;
  @JsonKey(name: 'achieved_at')
  final DateTime achievedAt;
  
  // Joined data
  final Medal? medal;

  const UserMedal({
    required this.id,
    required this.userId,
    required this.medalId,
    required this.achievedAt,
    this.medal,
  });

  factory UserMedal.fromJson(Map<String, dynamic> json) => _$UserMedalFromJson(json);
  Map<String, dynamic> toJson() => _$UserMedalToJson(this);

  UserMedal copyWith({
    String? id,
    String? userId,
    int? medalId,
    DateTime? achievedAt,
    Medal? medal,
  }) {
    return UserMedal(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      medalId: medalId ?? this.medalId,
      achievedAt: achievedAt ?? this.achievedAt,
      medal: medal ?? this.medal,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserMedal &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserMedal{id: $id, medalId: $medalId, achievedAt: $achievedAt}';
  }
}

// Predefined medals
class PredefinedMedals {
  static const List<Map<String, dynamic>> medals = [
    {
      'id': 1,
      'name': '第一口瓜',
      'description': '发布了第一个故事',
      'icon_url': 'assets/icons/first_melon.png',
    },
    {
      'id': 2,
      'name': '勤劳的猹',
      'description': '连续7天发布故事',
      'icon_url': 'assets/icons/hardworking_zha.png',
    },
    {
      'id': 3,
      'name': '瓜田守护者',
      'description': '获得100个赞',
      'icon_url': 'assets/icons/guardian.png',
    },
    {
      'id': 4,
      'name': '热瓜制造机',
      'description': '发布的故事总热度达到1000',
      'icon_url': 'assets/icons/hot_maker.png',
    },
    {
      'id': 5,
      'name': '吃瓜群众',
      'description': '对100个故事进行了投票',
      'icon_url': 'assets/icons/voter.png',
    },
    {
      'id': 6,
      'name': '表情包大师',
      'description': '使用了所有类型的emoji反应',
      'icon_url': 'assets/icons/emoji_master.png',
    },
    {
      'id': 7,
      'name': '瓜田新人',
      'description': '注册账号',
      'icon_url': 'assets/icons/newcomer.png',
    },
    {
      'id': 8,
      'name': '等级达人',
      'description': '达到10级',
      'icon_url': 'assets/icons/level_master.png',
    },
  ];
}
