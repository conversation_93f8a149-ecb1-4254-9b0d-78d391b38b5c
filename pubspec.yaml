name: guatian
description: A story-sharing platform with voting and gamification features (瓜田)
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # Supabase for backend
  supabase_flutter: ^2.3.4
  
  # State management
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^12.1.3
  
  # UI & Animations
  flutter_animate: ^4.5.0
  lottie: ^3.0.0
  
  # Networking & JSON
  http: ^1.1.2
  json_annotation: ^4.8.1
  
  # Utilities
  uuid: ^4.2.1
  intl: ^0.19.0
  shared_preferences: ^2.2.2
  flutter_dotenv: ^5.1.0
  
  # Icons & Fonts
  google_fonts: ^6.1.0
  feather_icons: ^1.2.0
  
  # Image handling
  cached_network_image: ^3.3.1
  
  # Development
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  # Assets
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
    - .env
  
  # Fonts
  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Bold.ttf
          weight: 700
        - asset: assets/fonts/Montserrat-ExtraBold.ttf
          weight: 800
    - family: NotoSansSC
      fonts:
        - asset: assets/fonts/NotoSansSC-Regular.ttf
        - asset: assets/fonts/NotoSansSC-Medium.ttf
          weight: 500
        - asset: assets/fonts/NotoSansSC-Bold.ttf
          weight: 700
